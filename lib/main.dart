import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'providers/inspection_provider.dart';
import 'screens/home_screen.dart';
import 'screens/inspection_list_screen.dart';
import 'screens/new_inspection_screen.dart';
import 'screens/inspection_form_screen.dart';
import 'screens/settings_screen.dart';
import 'theme/app_theme.dart';

void main() {
  runApp(const BOInspectionApp());
}

class BOInspectionApp extends StatelessWidget {
  const BOInspectionApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => InspectionProvider()),
      ],
      child: MaterialApp(
        title: 'BO Inspection Questionnaire',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        home: const HomeScreen(),
        routes: {
          '/home': (context) => const HomeScreen(),
          '/inspections': (context) => const InspectionListScreen(),
          '/new-inspection': (context) => const NewInspectionScreen(),
          '/settings': (context) => const SettingsScreen(),
        },
        onGenerateRoute: (settings) {
          if (settings.name == '/inspection-form') {
            final args = settings.arguments as Map<String, dynamic>?;
            return MaterialPageRoute(
              builder: (context) => InspectionFormScreen(
                inspectionId: args?['inspectionId'],
                isReadOnly: args?['isReadOnly'] ?? false,
              ),
            );
          }
          return null;
        },
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

class AppRoutes {
  static const String home = '/home';
  static const String inspections = '/inspections';
  static const String newInspection = '/new-inspection';
  static const String inspectionForm = '/inspection-form';
  static const String settings = '/settings';

  static void navigateToInspectionForm(
    BuildContext context, {
    String? inspectionId,
    bool isReadOnly = false,
  }) {
    Navigator.pushNamed(
      context,
      inspectionForm,
      arguments: {
        'inspectionId': inspectionId,
        'isReadOnly': isReadOnly,
      },
    );
  }

  static void navigateToHome(BuildContext context) {
    Navigator.pushNamedAndRemoveUntil(
      context,
      home,
      (route) => false,
    );
  }

  static void navigateToInspections(BuildContext context) {
    Navigator.pushNamed(context, inspections);
  }

  static void navigateToNewInspection(BuildContext context) {
    Navigator.pushNamed(context, newInspection);
  }

  static void navigateToSettings(BuildContext context) {
    Navigator.pushNamed(context, settings);
  }
}

class AppConstants {
  // App Information
  static const String appName = 'BO Inspection Questionnaire';
  static const String appVersion = '1.0.0';
  static const String appDescription = 
      'Branch Post Office Inspection Questionnaire - A comprehensive data entry application for postal inspection management.';

  // Date Formats
  static final DateFormat dateFormat = DateFormat('dd/MM/yyyy');
  static final DateFormat dateTimeFormat = DateFormat('dd/MM/yyyy HH:mm');
  static final DateFormat timeFormat = DateFormat('HH:mm');

  // File Export
  static const String csvFileName = 'bo_inspection_export.csv';
  static const String jsonFileName = 'bo_inspection_export.json';
  static const String pdfFileName = 'bo_inspection_report.pdf';

  // Validation Messages
  static const String requiredFieldMessage = 'This field is required';
  static const String invalidEmailMessage = 'Please enter a valid email';
  static const String invalidNumberMessage = 'Please enter a valid number';
  static const String invalidDateMessage = 'Please enter a valid date';

  // Section Names
  static const List<String> sectionNames = [
    'A. Administration',
    'B. Technology and DARPAN',
    'C. Mails',
    'D. Finance and Accounting',
    'E. Savings Bank',
    'F. Postal Life Insurance/RPLI',
    'G. Grievance Handling and Public Relations',
    'H. Business Development',
    'I. Training',
    'J. India Post Payment Bank (IPPB)',
    'K. Conclusion',
  ];

  // Section Icons
  static const List<IconData> sectionIcons = [
    Icons.admin_panel_settings,
    Icons.computer,
    Icons.mail,
    Icons.account_balance,
    Icons.savings,
    Icons.security,
    Icons.support_agent,
    Icons.business,
    Icons.school,
    Icons.payment,
    Icons.assignment_turned_in,
  ];

  // Colors
  static const Color primaryColor = Color(0xFF1976D2);
  static const Color secondaryColor = Color(0xFF388E3C);
  static const Color errorColor = Color(0xFFD32F2F);
  static const Color warningColor = Color(0xFFF57C00);
  static const Color successColor = Color(0xFF388E3C);

  // Spacing
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // Border Radius
  static const double borderRadiusSmall = 4.0;
  static const double borderRadiusMedium = 8.0;
  static const double borderRadiusLarge = 12.0;

  // Animation Durations
  static const Duration animationDurationShort = Duration(milliseconds: 200);
  static const Duration animationDurationMedium = Duration(milliseconds: 300);
  static const Duration animationDurationLong = Duration(milliseconds: 500);

  // Database
  static const String databaseName = 'bo_inspection.db';
  static const int databaseVersion = 1;

  // Export Formats
  static const List<String> exportFormats = ['CSV', 'JSON', 'PDF'];

  // Grade Thresholds
  static const Map<String, double> gradeThresholds = {
    'Excellent': 90.0,
    'Very Good': 80.0,
    'Good': 70.0,
    'Satisfactory': 60.0,
    'Needs Improvement': 50.0,
    'Poor': 0.0,
  };

  // Default Values
  static const double defaultCashBalance = 5000.0;
  static const int defaultSignalStrength = 75;
  static const int defaultKnowledgeLevel = 3;

  // File Size Limits (in bytes)
  static const int maxFileSize = 10 * 1024 * 1024; // 10 MB
  static const int maxImageSize = 5 * 1024 * 1024; // 5 MB

  // Network Timeouts
  static const Duration networkTimeout = Duration(seconds: 30);
  static const Duration connectionTimeout = Duration(seconds: 10);
}

class AppUtils {
  static String formatDate(DateTime date) {
    return AppConstants.dateFormat.format(date);
  }

  static String formatDateTime(DateTime dateTime) {
    return AppConstants.dateTimeFormat.format(dateTime);
  }

  static DateTime? parseDate(String dateString) {
    try {
      return AppConstants.dateFormat.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  static Color getGradeColor(String grade) {
    switch (grade.toLowerCase()) {
      case 'excellent':
        return Colors.green[700]!;
      case 'very good':
        return Colors.green[500]!;
      case 'good':
        return Colors.lightGreen[600]!;
      case 'satisfactory':
        return Colors.orange[600]!;
      case 'needs improvement':
        return Colors.orange[800]!;
      case 'poor':
        return Colors.red[700]!;
      default:
        return Colors.grey[600]!;
    }
  }

  static IconData getGradeIcon(String grade) {
    switch (grade.toLowerCase()) {
      case 'excellent':
        return Icons.star;
      case 'very good':
        return Icons.thumb_up;
      case 'good':
        return Icons.check_circle;
      case 'satisfactory':
        return Icons.check;
      case 'needs improvement':
        return Icons.warning;
      case 'poor':
        return Icons.error;
      default:
        return Icons.help;
    }
  }

  static void showSnackBar(BuildContext context, String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppConstants.errorColor : AppConstants.successColor,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'Dismiss',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  static Future<bool> showConfirmDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Confirm'),
          ),
        ],
      ),
    ) ?? false;
  }
}
