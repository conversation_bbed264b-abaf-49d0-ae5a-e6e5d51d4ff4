import 'package:json_annotation/json_annotation.dart';

part 'section_models.g.dart';

// Section B: Technology and DARPAN
@JsonSerializable()
class TechnologyData {
  final List<DeviceInfo> devices;
  final List<ApplicationInfo> applications;
  final int signalStrength;
  final bool connectivityIssues;
  final bool solarPanelFunctioning;
  final bool batteryCradlesWorking;
  final UserInfo userInfo;
  final DeviceInfo deviceInfo;
  final bool networkConnectivitySatisfactory;
  final bool solarInstallationReportAvailable;
  final bool bpmAwareOfIncidentProcess;
  final bool hardwareIssuesResolved;
  final bool deviceKeptProperly;
  final int bpmKnowledgeLevel; // 1-5 scale
  final String? technologyFeedback;

  TechnologyData({
    this.devices = const [],
    this.applications = const [],
    required this.signalStrength,
    required this.connectivityIssues,
    required this.solarPanelFunctioning,
    required this.batteryCradlesWorking,
    required this.userInfo,
    required this.deviceInfo,
    required this.networkConnectivitySatisfactory,
    required this.solarInstallationReportAvailable,
    required this.bpmAwareOfIncidentProcess,
    required this.hardwareIssuesResolved,
    required this.deviceKeptProperly,
    required this.bpmKnowledgeLevel,
    this.technologyFeedback,
  });

  factory TechnologyData.fromJson(Map<String, dynamic> json) =>
      _$TechnologyDataFromJson(json);

  Map<String, dynamic> toJson() => _$TechnologyDataToJson(this);
}

@JsonSerializable()
class DeviceInfo {
  final String deviceType;
  final String serialNumber;
  final String? imeiNumber;
  final String? simType;

  DeviceInfo({
    required this.deviceType,
    required this.serialNumber,
    this.imeiNumber,
    this.simType,
  });

  factory DeviceInfo.fromJson(Map<String, dynamic> json) =>
      _$DeviceInfoFromJson(json);

  Map<String, dynamic> toJson() => _$DeviceInfoToJson(this);
}

@JsonSerializable()
class ApplicationInfo {
  final String applicationName;
  final String version;
  final bool isUpdated;

  ApplicationInfo({
    required this.applicationName,
    required this.version,
    required this.isUpdated,
  });

  factory ApplicationInfo.fromJson(Map<String, dynamic> json) =>
      _$ApplicationInfoFromJson(json);

  Map<String, dynamic> toJson() => _$ApplicationInfoToJson(this);
}

@JsonSerializable()
class UserInfo {
  final String boName;
  final String facilityId;
  final String userName;
  final String userId;
  final String userRole;

  UserInfo({
    required this.boName,
    required this.facilityId,
    required this.userName,
    required this.userId,
    required this.userRole,
  });

  factory UserInfo.fromJson(Map<String, dynamic> json) =>
      _$UserInfoFromJson(json);

  Map<String, dynamic> toJson() => _$UserInfoToJson(this);
}

// Section C: Mails
@JsonSerializable()
class MailsData {
  final bool prescribedProcessFollowed;
  final bool bagDetailsAvailable;
  final bool bpmIssuingAccountableArticles;
  final List<ArticleInDeposit> articlesInDeposit;
  final bool vpmoBookedCorrectly;
  final int numberOfLetterBoxes;
  final bool letterBoxesInGoodCondition;
  final bool clearancePunctual;
  final bool mailsIncludedInDispatch;
  final String workingHoursReview;
  final bool transitNormsComplied;
  final String? delayInvestigation;

  MailsData({
    required this.prescribedProcessFollowed,
    required this.bagDetailsAvailable,
    required this.bpmIssuingAccountableArticles,
    this.articlesInDeposit = const [],
    required this.vpmoBookedCorrectly,
    required this.numberOfLetterBoxes,
    required this.letterBoxesInGoodCondition,
    required this.clearancePunctual,
    required this.mailsIncludedInDispatch,
    required this.workingHoursReview,
    required this.transitNormsComplied,
    this.delayInvestigation,
  });

  factory MailsData.fromJson(Map<String, dynamic> json) =>
      _$MailsDataFromJson(json);

  Map<String, dynamic> toJson() => _$MailsDataToJson(this);
}

@JsonSerializable()
class ArticleInDeposit {
  final String articleNumber;
  final String articleType;
  final DateTime receivedDate;
  final bool detainedBeyondPeriod;
  final String condition;
  final String remarks;

  ArticleInDeposit({
    required this.articleNumber,
    required this.articleType,
    required this.receivedDate,
    required this.detainedBeyondPeriod,
    required this.condition,
    required this.remarks,
  });

  factory ArticleInDeposit.fromJson(Map<String, dynamic> json) =>
      _$ArticleInDepositFromJson(json);

  Map<String, dynamic> toJson() => _$ArticleInDepositToJson(this);
}

// Section D: Finance and Accounting
@JsonSerializable()
class FinanceData {
  final double lastAcknowledgedBalance;
  final double currentCashBalance;
  final double walletBalance;
  final bool balancesMatch;
  final StampInventory stampInventory;
  final bool stampsReceivedThroughCSI;
  final bool stampSalesUpdatedInstantly;
  final bool authorizedBalancesAdequate;
  final List<MoneyOrderTransaction> moneyOrders;
  final bool moPaymentPrompt;
  final bool excessCashRetained;
  final bool cashExchangeSatisfactory;
  final bool dailyAccountGenerated;
  final bool articlesProperlyStamped;
  final bool cashBagProperlySealed;

  FinanceData({
    required this.lastAcknowledgedBalance,
    required this.currentCashBalance,
    required this.walletBalance,
    required this.balancesMatch,
    required this.stampInventory,
    required this.stampsReceivedThroughCSI,
    required this.stampSalesUpdatedInstantly,
    required this.authorizedBalancesAdequate,
    this.moneyOrders = const [],
    required this.moPaymentPrompt,
    required this.excessCashRetained,
    required this.cashExchangeSatisfactory,
    required this.dailyAccountGenerated,
    required this.articlesProperlyStamped,
    required this.cashBagProperlySealed,
  });

  factory FinanceData.fromJson(Map<String, dynamic> json) =>
      _$FinanceDataFromJson(json);

  Map<String, dynamic> toJson() => _$FinanceDataToJson(this);
}

@JsonSerializable()
class StampInventory {
  final Map<String, int> stampDenominations;
  final double totalValue;
  final bool inventoryMatches;

  StampInventory({
    this.stampDenominations = const {},
    required this.totalValue,
    required this.inventoryMatches,
  });

  factory StampInventory.fromJson(Map<String, dynamic> json) =>
      _$StampInventoryFromJson(json);

  Map<String, dynamic> toJson() => _$StampInventoryToJson(this);
}

@JsonSerializable()
class MoneyOrderTransaction {
  final String moNumber;
  final double amount;
  final DateTime issueDate;
  final DateTime? paymentDate;
  final String status;
  final String? remarks;

  MoneyOrderTransaction({
    required this.moNumber,
    required this.amount,
    required this.issueDate,
    this.paymentDate,
    required this.status,
    this.remarks,
  });

  factory MoneyOrderTransaction.fromJson(Map<String, dynamic> json) =>
      _$MoneyOrderTransactionFromJson(json);

  Map<String, dynamic> toJson() => _$MoneyOrderTransactionToJson(this);
}
