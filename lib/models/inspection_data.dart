import 'package:json_annotation/json_annotation.dart';

part 'inspection_data.g.dart';

@JsonSerializable()
class InspectionData {
  final String id;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String inspectorName;
  final String inspectorDesignation;
  final DateTime inspectionDate;
  final bool isCompleted;
  
  // Section A: Administration
  final AdministrationData administration;
  
  // Section B: Technology and DARPAN
  final TechnologyData technology;
  
  // Section C: Mails
  final MailsData mails;
  
  // Section D: Finance and Accounting
  final FinanceData finance;
  
  // Section E: Savings Bank
  final SavingsBankData savingsBank;
  
  // Section F: Postal Life Insurance/RPLI
  final InsuranceData insurance;
  
  // Section G: Grievance Handling
  final GrievanceData grievance;
  
  // Section H: Business Development
  final BusinessData business;
  
  // Section I: Training
  final TrainingData training;
  
  // Section J: India Post Payment Bank
  final IPPBData ippb;
  
  // Section K: Conclusion
  final ConclusionData conclusion;

  InspectionData({
    required this.id,
    required this.createdAt,
    this.updatedAt,
    required this.inspectorName,
    required this.inspectorDesignation,
    required this.inspectionDate,
    this.isCompleted = false,
    required this.administration,
    required this.technology,
    required this.mails,
    required this.finance,
    required this.savingsBank,
    required this.insurance,
    required this.grievance,
    required this.business,
    required this.training,
    required this.ippb,
    required this.conclusion,
  });

  factory InspectionData.fromJson(Map<String, dynamic> json) =>
      _$InspectionDataFromJson(json);

  Map<String, dynamic> toJson() => _$InspectionDataToJson(this);

  InspectionData copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? inspectorName,
    String? inspectorDesignation,
    DateTime? inspectionDate,
    bool? isCompleted,
    AdministrationData? administration,
    TechnologyData? technology,
    MailsData? mails,
    FinanceData? finance,
    SavingsBankData? savingsBank,
    InsuranceData? insurance,
    GrievanceData? grievance,
    BusinessData? business,
    TrainingData? training,
    IPPBData? ippb,
    ConclusionData? conclusion,
  }) {
    return InspectionData(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      inspectorName: inspectorName ?? this.inspectorName,
      inspectorDesignation: inspectorDesignation ?? this.inspectorDesignation,
      inspectionDate: inspectionDate ?? this.inspectionDate,
      isCompleted: isCompleted ?? this.isCompleted,
      administration: administration ?? this.administration,
      technology: technology ?? this.technology,
      mails: mails ?? this.mails,
      finance: finance ?? this.finance,
      savingsBank: savingsBank ?? this.savingsBank,
      insurance: insurance ?? this.insurance,
      grievance: grievance ?? this.grievance,
      business: business ?? this.business,
      training: training ?? this.training,
      ippb: ippb ?? this.ippb,
      conclusion: conclusion ?? this.conclusion,
    );
  }
}

// Section A: Administration Data Model
@JsonSerializable()
class AdministrationData {
  // General Information
  final String branchOfficeName;
  final String accountOfficeName;
  final String headOfficeName;
  final String facilityId;
  final String profitCostCentreId;
  final DateTime? dateOfOpening;
  final DateTime? lastInspectionDate;
  final String? lastInspectedBy;
  final List<DateTime> subsequentVisitsBySubDivisionalHead;
  final List<DateTime> subsequentVisitsByMailOverseer;
  
  // Technical Information
  final String workingHours;
  final double authorizedCashBalance;
  final double minimumCashBalance;
  final double maximumCashBalance;
  final double authorizedStampBalance;
  final String location;
  final List<String> villagesServed;
  final String panchayatHeadQuarters;
  final String mailArrangements;
  
  // Establishment Information
  final List<StaffMember> staff;
  final DateTime? lastEstablishmentReview;
  final String establishmentReviewResults;
  final bool mailOverseerVisitsEffective;
  final bool stockVerificationSatisfactory;
  final List<String> infrastructureItems;
  final bool stationaryAdequate;
  final bool registersProperlyMaintained;
  final String? previousInspectionPendingParas;

  AdministrationData({
    required this.branchOfficeName,
    required this.accountOfficeName,
    required this.headOfficeName,
    required this.facilityId,
    required this.profitCostCentreId,
    this.dateOfOpening,
    this.lastInspectionDate,
    this.lastInspectedBy,
    this.subsequentVisitsBySubDivisionalHead = const [],
    this.subsequentVisitsByMailOverseer = const [],
    required this.workingHours,
    required this.authorizedCashBalance,
    required this.minimumCashBalance,
    required this.maximumCashBalance,
    required this.authorizedStampBalance,
    required this.location,
    this.villagesServed = const [],
    required this.panchayatHeadQuarters,
    required this.mailArrangements,
    this.staff = const [],
    this.lastEstablishmentReview,
    required this.establishmentReviewResults,
    required this.mailOverseerVisitsEffective,
    required this.stockVerificationSatisfactory,
    this.infrastructureItems = const [],
    required this.stationaryAdequate,
    required this.registersProperlyMaintained,
    this.previousInspectionPendingParas,
  });

  factory AdministrationData.fromJson(Map<String, dynamic> json) =>
      _$AdministrationDataFromJson(json);

  Map<String, dynamic> toJson() => _$AdministrationDataToJson(this);
}

@JsonSerializable()
class StaffMember {
  final String name;
  final String employeeId;
  final String designation;
  final String trcaNumber;
  final DateTime dateOfBirth;
  final DateTime dateOfAppointment;
  final String remarks;

  StaffMember({
    required this.name,
    required this.employeeId,
    required this.designation,
    required this.trcaNumber,
    required this.dateOfBirth,
    required this.dateOfAppointment,
    required this.remarks,
  });

  factory StaffMember.fromJson(Map<String, dynamic> json) =>
      _$StaffMemberFromJson(json);

  Map<String, dynamic> toJson() => _$StaffMemberToJson(this);
}
