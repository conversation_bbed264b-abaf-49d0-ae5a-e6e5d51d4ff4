import 'package:json_annotation/json_annotation.dart';

part 'remaining_sections.g.dart';

// Section E: Savings Bank
@JsonSerializable()
class SavingsBankData {
  final List<SBReceipt> sbReceipts;
  final bool specimenSignatureBooksProper;
  final List<PassbookVerification> passbookVerifications;
  final bool undeliveredPassbooksDetained;
  final List<TDAccount> tdAccounts;
  final bool commissionCalculatedCorrectly;
  final bool bpmKnowsAllSchemes;
  final int ippbAccountsOpened;
  final QRCardStock qrCardStock;
  final int socialSecurityPayments;
  final int merchantIPPBAccounts;
  final bool bpmAwareOfIPPBApp;

  SavingsBankData({
    this.sbReceipts = const [],
    required this.specimenSignatureBooksProper,
    this.passbookVerifications = const [],
    required this.undeliveredPassbooksDetained,
    this.tdAccounts = const [],
    required this.commissionCalculatedCorrectly,
    required this.bpmKnowsAllSchemes,
    required this.ippbAccountsOpened,
    required this.qrCardStock,
    required this.socialSecurityPayments,
    required this.merchantIPPBAccounts,
    required this.bpmAwareOfIPPBApp,
  });

  factory SavingsBankData.fromJson(Map<String, dynamic> json) =>
      _$SavingsBankDataFromJson(json);

  Map<String, dynamic> toJson() => _$SavingsBankDataToJson(this);
}

@JsonSerializable()
class SBReceipt {
  final String receiptNumber;
  final double amount;
  final DateTime issueDate;
  final bool originalReceiptAvailable;
  final String accountNumber;

  SBReceipt({
    required this.receiptNumber,
    required this.amount,
    required this.issueDate,
    required this.originalReceiptAvailable,
    required this.accountNumber,
  });

  factory SBReceipt.fromJson(Map<String, dynamic> json) =>
      _$SBReceiptFromJson(json);

  Map<String, dynamic> toJson() => _$SBReceiptToJson(this);
}

@JsonSerializable()
class PassbookVerification {
  final String accountNumber;
  final String accountType;
  final String depositorName;
  final double balance;
  final bool balanceMatches;
  final DateTime verificationDate;

  PassbookVerification({
    required this.accountNumber,
    required this.accountType,
    required this.depositorName,
    required this.balance,
    required this.balanceMatches,
    required this.verificationDate,
  });

  factory PassbookVerification.fromJson(Map<String, dynamic> json) =>
      _$PassbookVerificationFromJson(json);

  Map<String, dynamic> toJson() => _$PassbookVerificationToJson(this);
}

@JsonSerializable()
class TDAccount {
  final String accountNumber;
  final double amount;
  final DateTime maturityDate;
  final bool prematurelyClosed;
  final String? closureReason;

  TDAccount({
    required this.accountNumber,
    required this.amount,
    required this.maturityDate,
    required this.prematurelyClosed,
    this.closureReason,
  });

  factory TDAccount.fromJson(Map<String, dynamic> json) =>
      _$TDAccountFromJson(json);

  Map<String, dynamic> toJson() => _$TDAccountToJson(this);
}

@JsonSerializable()
class QRCardStock {
  final int totalReceived;
  final int currentStock;
  final int duplicatesIssued;
  final bool stockRegisterMaintained;

  QRCardStock({
    required this.totalReceived,
    required this.currentStock,
    required this.duplicatesIssued,
    required this.stockRegisterMaintained,
  });

  factory QRCardStock.fromJson(Map<String, dynamic> json) =>
      _$QRCardStockFromJson(json);

  Map<String, dynamic> toJson() => _$QRCardStockToJson(this);
}

// Section F: Postal Life Insurance/RPLI
@JsonSerializable()
class InsuranceData {
  final bool bpmAwareOfSchemes;
  final bool deviceSupplied;
  final bool bpmTrained;
  final bool networkFunctioning;
  final bool logBookMaintained;
  final bool formsAvailable;
  final int proposalsIndexed;
  final int claimCases;
  final int serviceRequests;
  final List<PremiumCollection> premiumCollections;
  final bool dayEndReportGenerated;
  final List<PRBVerification> prbVerifications;
  final double growthRate;
  final bool targetAchieved;
  final bool incentivePaid;
  final bool publicityDisplayed;
  final bool revisedFormsAvailable;

  InsuranceData({
    required this.bpmAwareOfSchemes,
    required this.deviceSupplied,
    required this.bpmTrained,
    required this.networkFunctioning,
    required this.logBookMaintained,
    required this.formsAvailable,
    required this.proposalsIndexed,
    required this.claimCases,
    required this.serviceRequests,
    this.premiumCollections = const [],
    required this.dayEndReportGenerated,
    this.prbVerifications = const [],
    required this.growthRate,
    required this.targetAchieved,
    required this.incentivePaid,
    required this.publicityDisplayed,
    required this.revisedFormsAvailable,
  });

  factory InsuranceData.fromJson(Map<String, dynamic> json) =>
      _$InsuranceDataFromJson(json);

  Map<String, dynamic> toJson() => _$InsuranceDataToJson(this);
}

@JsonSerializable()
class PremiumCollection {
  final String policyNumber;
  final double amount;
  final DateTime collectionDate;
  final String type; // new/renewal
  final bool correctlyAccounted;

  PremiumCollection({
    required this.policyNumber,
    required this.amount,
    required this.collectionDate,
    required this.type,
    required this.correctlyAccounted,
  });

  factory PremiumCollection.fromJson(Map<String, dynamic> json) =>
      _$PremiumCollectionFromJson(json);

  Map<String, dynamic> toJson() => _$PremiumCollectionToJson(this);
}

@JsonSerializable()
class PRBVerification {
  final String prbNumber;
  final DateTime date;
  final double amount;
  final bool entriesCorrect;
  final bool signedAndStamped;

  PRBVerification({
    required this.prbNumber,
    required this.date,
    required this.amount,
    required this.entriesCorrect,
    required this.signedAndStamped,
  });

  factory PRBVerification.fromJson(Map<String, dynamic> json) =>
      _$PRBVerificationFromJson(json);

  Map<String, dynamic> toJson() => _$PRBVerificationToJson(this);
}

// Section G: Grievance Handling
@JsonSerializable()
class GrievanceData {
  final bool bpmKnowsRedressalMechanisms;
  final bool complaintsForwardedToCCC;
  final int complaintsReceived;
  final int complaintsResolved;
  final bool postInfoAppInstalled;
  final bool ePostOfficeAppInstalled;
  final bool postOfficeBoardDisplayed;
  final bool businessHoursDisplayed;

  GrievanceData({
    required this.bpmKnowsRedressalMechanisms,
    required this.complaintsForwardedToCCC,
    required this.complaintsReceived,
    required this.complaintsResolved,
    required this.postInfoAppInstalled,
    required this.ePostOfficeAppInstalled,
    required this.postOfficeBoardDisplayed,
    required this.businessHoursDisplayed,
  });

  factory GrievanceData.fromJson(Map<String, dynamic> json) =>
      _$GrievanceDataFromJson(json);

  Map<String, dynamic> toJson() => _$GrievanceDataToJson(this);
}

// Section H: Business Development
@JsonSerializable()
class BusinessData {
  final Map<String, BusinessMetrics> businessTypes;
  final bool targetsFixed;
  final double overallPerformance;
  final List<VillageBusinessInfo> villageInfo;
  final bool businessRegisterMaintained;

  BusinessData({
    this.businessTypes = const {},
    required this.targetsFixed,
    required this.overallPerformance,
    this.villageInfo = const [],
    required this.businessRegisterMaintained,
  });

  factory BusinessData.fromJson(Map<String, dynamic> json) =>
      _$BusinessDataFromJson(json);

  Map<String, dynamic> toJson() => _$BusinessDataToJson(this);
}

@JsonSerializable()
class BusinessMetrics {
  final int previousYearCount;
  final double previousYearAmount;
  final int currentYearCount;
  final double currentYearAmount;
  final String remarks;

  BusinessMetrics({
    required this.previousYearCount,
    required this.previousYearAmount,
    required this.currentYearCount,
    required this.currentYearAmount,
    required this.remarks,
  });

  factory BusinessMetrics.fromJson(Map<String, dynamic> json) =>
      _$BusinessMetricsFromJson(json);

  Map<String, dynamic> toJson() => _$BusinessMetricsToJson(this);
}

@JsonSerializable()
class VillageBusinessInfo {
  final String villageName;
  final int households;
  final int population;
  final int femalePopulation;
  final int sbAccounts;
  final int rdAccounts;
  final int ssaAccounts;
  final int rpliPolicies;

  VillageBusinessInfo({
    required this.villageName,
    required this.households,
    required this.population,
    required this.femalePopulation,
    required this.sbAccounts,
    required this.rdAccounts,
    required this.ssaAccounts,
    required this.rpliPolicies,
  });

  factory VillageBusinessInfo.fromJson(Map<String, dynamic> json) =>
      _$VillageBusinessInfoFromJson(json);

  Map<String, dynamic> toJson() => _$VillageBusinessInfoToJson(this);
}
