import 'package:json_annotation/json_annotation.dart';

part 'final_sections.g.dart';

// Section I: Training
@JsonSerializable()
class TrainingData {
  final bool inductionTrainingCompleted;
  final DateTime? inductionTrainingDate;
  final bool inductionCertificateAvailable;
  final bool refresherTrainingCompleted;
  final DateTime? refresherTrainingDate;
  final bool refresherCertificateAvailable;
  final String? trainingRemarks;

  TrainingData({
    required this.inductionTrainingCompleted,
    this.inductionTrainingDate,
    required this.inductionCertificateAvailable,
    required this.refresherTrainingCompleted,
    this.refresherTrainingDate,
    required this.refresherCertificateAvailable,
    this.trainingRemarks,
  });

  factory TrainingData.fromJson(Map<String, dynamic> json) =>
      _$TrainingDataFromJson(json);

  Map<String, dynamic> toJson() => _$TrainingDataToJson(this);
}

// Section J: India Post Payment Bank (IPPB)
@JsonSerializable()
class IPPBData {
  final bool facilitatingTransactionsDuringBusinessHours;
  final bool brandingElementsDisplayed;
  final bool suitableStaffingArrangements;
  final bool devicesInWorkingCondition;
  final bool properMobileConnectivity;
  final bool hardwareAccountedFor;
  final bool endUserProperlyTrained;
  final bool customerDocumentsRetained;
  final bool adequateCashProvided;
  final bool doorstepBankingCashAdequate;
  final bool transactionsTally;
  final bool financialLiteracyCampsOrganized;
  final bool merchantAcquisitionFocused;
  final bool posaSavingsLinkagePromoted;
  final bool territoryManagerVisiting;
  final String? ippbOperationsRemarks;

  IPPBData({
    required this.facilitatingTransactionsDuringBusinessHours,
    required this.brandingElementsDisplayed,
    required this.suitableStaffingArrangements,
    required this.devicesInWorkingCondition,
    required this.properMobileConnectivity,
    required this.hardwareAccountedFor,
    required this.endUserProperlyTrained,
    required this.customerDocumentsRetained,
    required this.adequateCashProvided,
    required this.doorstepBankingCashAdequate,
    required this.transactionsTally,
    required this.financialLiteracyCampsOrganized,
    required this.merchantAcquisitionFocused,
    required this.posaSavingsLinkagePromoted,
    required this.territoryManagerVisiting,
    this.ippbOperationsRemarks,
  });

  factory IPPBData.fromJson(Map<String, dynamic> json) =>
      _$IPPBDataFromJson(json);

  Map<String, dynamic> toJson() => _$IPPBDataToJson(this);
}

// Section K: Conclusion
@JsonSerializable()
class ConclusionData {
  final int maintenanceAndHardwarePoints;
  final int rictApplicationPoints;
  final int ippbPoints;
  final int businessDevelopmentPoints;
  final int serviceStandardsPoints;
  final int basicMaintenancePoints;
  final int totalPoints;
  final int maxPoints;
  final double percentage;
  final String overallGrade;
  final String inspectionResult;
  final List<String> recommendations;
  final List<String> actionItems;
  final String? generalRemarks;

  ConclusionData({
    required this.maintenanceAndHardwarePoints,
    required this.rictApplicationPoints,
    required this.ippbPoints,
    required this.businessDevelopmentPoints,
    required this.serviceStandardsPoints,
    required this.basicMaintenancePoints,
    required this.totalPoints,
    required this.maxPoints,
    required this.percentage,
    required this.overallGrade,
    required this.inspectionResult,
    this.recommendations = const [],
    this.actionItems = const [],
    this.generalRemarks,
  });

  factory ConclusionData.fromJson(Map<String, dynamic> json) =>
      _$ConclusionDataFromJson(json);

  Map<String, dynamic> toJson() => _$ConclusionDataToJson(this);

  // Helper method to calculate grade based on percentage
  static String calculateGrade(double percentage) {
    if (percentage >= 90) return 'Excellent';
    if (percentage >= 80) return 'Very Good';
    if (percentage >= 70) return 'Good';
    if (percentage >= 60) return 'Satisfactory';
    if (percentage >= 50) return 'Needs Improvement';
    return 'Poor';
  }

  // Helper method to calculate total points
  static int calculateTotalPoints({
    required int maintenanceAndHardware,
    required int rictApplication,
    required int ippb,
    required int businessDevelopment,
    required int serviceStandards,
    required int basicMaintenance,
  }) {
    return maintenanceAndHardware +
        rictApplication +
        ippb +
        businessDevelopment +
        serviceStandards +
        basicMaintenance;
  }

  // Helper method to calculate percentage
  static double calculatePercentage(int totalPoints, int maxPoints) {
    if (maxPoints == 0) return 0.0;
    return (totalPoints / maxPoints) * 100;
  }
}

// Official Language Data (Part of Administration but separate for clarity)
@JsonSerializable()
class OfficialLanguageData {
  final bool namePlatesTriLingual;
  final bool namePlatesAccordingToRules;
  final bool formsBilingual;
  final String? specialHindiWork;

  OfficialLanguageData({
    required this.namePlatesTriLingual,
    required this.namePlatesAccordingToRules,
    required this.formsBilingual,
    this.specialHindiWork,
  });

  factory OfficialLanguageData.fromJson(Map<String, dynamic> json) =>
      _$OfficialLanguageDataFromJson(json);

  Map<String, dynamic> toJson() => _$OfficialLanguageDataToJson(this);
}

// Inspection Summary for quick overview
@JsonSerializable()
class InspectionSummary {
  final String id;
  final String branchOfficeName;
  final String inspectorName;
  final DateTime inspectionDate;
  final bool isCompleted;
  final double? totalScore;
  final String? grade;
  final DateTime createdAt;
  final DateTime? updatedAt;

  InspectionSummary({
    required this.id,
    required this.branchOfficeName,
    required this.inspectorName,
    required this.inspectionDate,
    required this.isCompleted,
    this.totalScore,
    this.grade,
    required this.createdAt,
    this.updatedAt,
  });

  factory InspectionSummary.fromJson(Map<String, dynamic> json) =>
      _$InspectionSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$InspectionSummaryToJson(this);
}

// Validation Rules for different fields
class ValidationRules {
  static const int maxBranchOfficeNameLength = 100;
  static const int maxInspectorNameLength = 50;
  static const int maxRemarksLength = 500;
  static const int maxFacilityIdLength = 20;
  static const int maxEmployeeIdLength = 15;
  
  static const double minCashBalance = 0.0;
  static const double maxCashBalance = 1000000.0;
  
  static const int minSignalStrength = 0;
  static const int maxSignalStrength = 100;
  
  static const int minKnowledgeLevel = 1;
  static const int maxKnowledgeLevel = 5;
  
  static const int maxPointsMaintenanceHardware = 40;
  static const int maxPointsRictApplication = 10;
  static const int maxPointsIppb = 20;
  static const int maxPointsBusinessDevelopment = 20;
  static const int maxPointsServiceStandards = 10;
  static const int maxPointsBasicMaintenance = 10;
  
  static const int totalMaxPoints = maxPointsMaintenanceHardware +
      maxPointsRictApplication +
      maxPointsIppb +
      maxPointsBusinessDevelopment +
      maxPointsServiceStandards +
      maxPointsBasicMaintenance;

  // Validation methods
  static bool isValidFacilityId(String facilityId) {
    return facilityId.isNotEmpty && facilityId.length <= maxFacilityIdLength;
  }

  static bool isValidEmployeeId(String employeeId) {
    return employeeId.isNotEmpty && employeeId.length <= maxEmployeeIdLength;
  }

  static bool isValidCashBalance(double balance) {
    return balance >= minCashBalance && balance <= maxCashBalance;
  }

  static bool isValidSignalStrength(int strength) {
    return strength >= minSignalStrength && strength <= maxSignalStrength;
  }

  static bool isValidKnowledgeLevel(int level) {
    return level >= minKnowledgeLevel && level <= maxKnowledgeLevel;
  }

  static bool isValidPoints(int points, int maxPoints) {
    return points >= 0 && points <= maxPoints;
  }
}
