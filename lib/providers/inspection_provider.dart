import 'package:flutter/foundation.dart';
import '../models/inspection_data.dart';
import '../models/section_models.dart';
import '../models/remaining_sections.dart';
import '../models/final_sections.dart';
import '../services/database_service.dart';

class InspectionProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  
  // Current inspection being edited
  InspectionData? _currentInspection;
  
  // List of all inspections
  List<InspectionSummary> _inspections = [];
  
  // Loading states
  bool _isLoading = false;
  bool _isSaving = false;
  
  // Error handling
  String? _errorMessage;
  
  // Statistics
  Map<String, dynamic>? _statistics;
  
  // Search and filter
  String _searchQuery = '';
  DateTime? _filterStartDate;
  DateTime? _filterEndDate;
  
  // Getters
  InspectionData? get currentInspection => _currentInspection;
  List<InspectionSummary> get inspections => _inspections;
  bool get isLoading => _isLoading;
  bool get isSaving => _isSaving;
  String? get errorMessage => _errorMessage;
  Map<String, dynamic>? get statistics => _statistics;
  String get searchQuery => _searchQuery;
  DateTime? get filterStartDate => _filterStartDate;
  DateTime? get filterEndDate => _filterEndDate;
  
  // Initialize provider
  Future<void> initialize() async {
    await loadInspections();
    await loadStatistics();
  }
  
  // Load all inspections
  Future<void> loadInspections() async {
    _setLoading(true);
    try {
      if (_searchQuery.isNotEmpty) {
        _inspections = await _databaseService.searchInspections(_searchQuery);
      } else if (_filterStartDate != null && _filterEndDate != null) {
        _inspections = await _databaseService.getInspectionSummariesByDateRange(
          _filterStartDate!,
          _filterEndDate!,
        );
      } else {
        _inspections = await _databaseService.getAllInspectionSummaries();
      }
      _clearError();
    } catch (e) {
      _setError('Failed to load inspections: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // Load statistics
  Future<void> loadStatistics() async {
    try {
      _statistics = await _databaseService.getInspectionStatistics();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load statistics: $e');
    }
  }
  
  // Create new inspection
  Future<String?> createNewInspection({
    required String inspectorName,
    required String inspectorDesignation,
    required DateTime inspectionDate,
    required String branchOfficeName,
    required String accountOfficeName,
    required String headOfficeName,
  }) async {
    _setSaving(true);
    try {
      final id = DateTime.now().millisecondsSinceEpoch.toString();
      
      final inspection = InspectionData(
        id: id,
        createdAt: DateTime.now(),
        inspectorName: inspectorName,
        inspectorDesignation: inspectorDesignation,
        inspectionDate: inspectionDate,
        administration: AdministrationData(
          branchOfficeName: branchOfficeName,
          accountOfficeName: accountOfficeName,
          headOfficeName: headOfficeName,
          facilityId: '',
          profitCostCentreId: '',
          workingHours: '',
          authorizedCashBalance: 0.0,
          minimumCashBalance: 0.0,
          maximumCashBalance: 0.0,
          authorizedStampBalance: 0.0,
          location: '',
          panchayatHeadQuarters: '',
          mailArrangements: '',
          establishmentReviewResults: '',
          mailOverseerVisitsEffective: false,
          stockVerificationSatisfactory: false,
          stationaryAdequate: false,
          registersProperlyMaintained: false,
        ),
        technology: TechnologyData(
          signalStrength: 0,
          connectivityIssues: false,
          solarPanelFunctioning: false,
          batteryCradlesWorking: false,
          userInfo: UserInfo(
            boName: branchOfficeName,
            facilityId: '',
            userName: '',
            userId: '',
            userRole: '',
          ),
          deviceInfo: DeviceInfo(
            deviceType: '',
            serialNumber: '',
          ),
          networkConnectivitySatisfactory: false,
          solarInstallationReportAvailable: false,
          bpmAwareOfIncidentProcess: false,
          hardwareIssuesResolved: false,
          deviceKeptProperly: false,
          bpmKnowledgeLevel: 1,
        ),
        mails: MailsData(
          prescribedProcessFollowed: false,
          bagDetailsAvailable: false,
          bpmIssuingAccountableArticles: false,
          vpmoBookedCorrectly: false,
          numberOfLetterBoxes: 0,
          letterBoxesInGoodCondition: false,
          clearancePunctual: false,
          mailsIncludedInDispatch: false,
          workingHoursReview: '',
          transitNormsComplied: false,
        ),
        finance: FinanceData(
          lastAcknowledgedBalance: 0.0,
          currentCashBalance: 0.0,
          walletBalance: 0.0,
          balancesMatch: false,
          stampInventory: StampInventory(
            totalValue: 0.0,
            inventoryMatches: false,
          ),
          stampsReceivedThroughCSI: false,
          stampSalesUpdatedInstantly: false,
          authorizedBalancesAdequate: false,
          moPaymentPrompt: false,
          excessCashRetained: false,
          cashExchangeSatisfactory: false,
          dailyAccountGenerated: false,
          articlesProperlyStamped: false,
          cashBagProperlySealed: false,
        ),
        savingsBank: SavingsBankData(
          specimenSignatureBooksProper: false,
          undeliveredPassbooksDetained: false,
          commissionCalculatedCorrectly: false,
          bpmKnowsAllSchemes: false,
          ippbAccountsOpened: 0,
          qrCardStock: QRCardStock(
            totalReceived: 0,
            currentStock: 0,
            duplicatesIssued: 0,
            stockRegisterMaintained: false,
          ),
          socialSecurityPayments: 0,
          merchantIPPBAccounts: 0,
          bpmAwareOfIPPBApp: false,
        ),
        insurance: InsuranceData(
          bpmAwareOfSchemes: false,
          deviceSupplied: false,
          bpmTrained: false,
          networkFunctioning: false,
          logBookMaintained: false,
          formsAvailable: false,
          proposalsIndexed: 0,
          claimCases: 0,
          serviceRequests: 0,
          dayEndReportGenerated: false,
          growthRate: 0.0,
          targetAchieved: false,
          incentivePaid: false,
          publicityDisplayed: false,
          revisedFormsAvailable: false,
        ),
        grievance: GrievanceData(
          bpmKnowsRedressalMechanisms: false,
          complaintsForwardedToCCC: false,
          complaintsReceived: 0,
          complaintsResolved: 0,
          postInfoAppInstalled: false,
          ePostOfficeAppInstalled: false,
          postOfficeBoardDisplayed: false,
          businessHoursDisplayed: false,
        ),
        business: BusinessData(
          targetsFixed: false,
          overallPerformance: 0.0,
          businessRegisterMaintained: false,
        ),
        training: TrainingData(
          inductionTrainingCompleted: false,
          inductionCertificateAvailable: false,
          refresherTrainingCompleted: false,
          refresherCertificateAvailable: false,
        ),
        ippb: IPPBData(
          facilitatingTransactionsDuringBusinessHours: false,
          brandingElementsDisplayed: false,
          suitableStaffingArrangements: false,
          devicesInWorkingCondition: false,
          properMobileConnectivity: false,
          hardwareAccountedFor: false,
          endUserProperlyTrained: false,
          customerDocumentsRetained: false,
          adequateCashProvided: false,
          doorstepBankingCashAdequate: false,
          transactionsTally: false,
          financialLiteracyCampsOrganized: false,
          merchantAcquisitionFocused: false,
          posaSavingsLinkagePromoted: false,
          territoryManagerVisiting: false,
        ),
        conclusion: ConclusionData(
          maintenanceAndHardwarePoints: 0,
          rictApplicationPoints: 0,
          ippbPoints: 0,
          businessDevelopmentPoints: 0,
          serviceStandardsPoints: 0,
          basicMaintenancePoints: 0,
          totalPoints: 0,
          maxPoints: ValidationRules.totalMaxPoints,
          percentage: 0.0,
          overallGrade: 'Not Assessed',
          inspectionResult: 'In Progress',
        ),
      );
      
      await _databaseService.saveInspection(inspection);
      _currentInspection = inspection;
      await loadInspections();
      await loadStatistics();
      _clearError();
      return id;
    } catch (e) {
      _setError('Failed to create inspection: $e');
      return null;
    } finally {
      _setSaving(false);
    }
  }
  
  // Load specific inspection
  Future<void> loadInspection(String id) async {
    _setLoading(true);
    try {
      _currentInspection = await _databaseService.getInspection(id);
      if (_currentInspection == null) {
        _setError('Inspection not found');
      } else {
        _clearError();
      }
    } catch (e) {
      _setError('Failed to load inspection: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // Save current inspection
  Future<bool> saveCurrentInspection() async {
    if (_currentInspection == null) return false;
    
    _setSaving(true);
    try {
      await _databaseService.updateInspection(_currentInspection!);
      await loadInspections();
      await loadStatistics();
      _clearError();
      return true;
    } catch (e) {
      _setError('Failed to save inspection: $e');
      return false;
    } finally {
      _setSaving(false);
    }
  }
  
  // Update current inspection
  void updateCurrentInspection(InspectionData inspection) {
    _currentInspection = inspection;
    notifyListeners();
  }
  
  // Delete inspection
  Future<bool> deleteInspection(String id) async {
    try {
      await _databaseService.deleteInspection(id);
      await loadInspections();
      await loadStatistics();
      if (_currentInspection?.id == id) {
        _currentInspection = null;
      }
      _clearError();
      return true;
    } catch (e) {
      _setError('Failed to delete inspection: $e');
      return false;
    }
  }
  
  // Search inspections
  Future<void> searchInspections(String query) async {
    _searchQuery = query;
    await loadInspections();
  }
  
  // Filter inspections by date range
  Future<void> filterInspectionsByDateRange(DateTime? startDate, DateTime? endDate) async {
    _filterStartDate = startDate;
    _filterEndDate = endDate;
    await loadInspections();
  }
  
  // Clear filters
  Future<void> clearFilters() async {
    _searchQuery = '';
    _filterStartDate = null;
    _filterEndDate = null;
    await loadInspections();
  }
  
  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setSaving(bool saving) {
    _isSaving = saving;
    notifyListeners();
  }
  
  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
  
  // Clear current inspection
  void clearCurrentInspection() {
    _currentInspection = null;
    notifyListeners();
  }
}
