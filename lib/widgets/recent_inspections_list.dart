import 'package:flutter/material.dart';
import '../models/final_sections.dart';
import '../main.dart';

class RecentInspectionsList extends StatelessWidget {
  final List<dynamic> inspections;

  const RecentInspectionsList({
    super.key,
    required this.inspections,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: inspections.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final inspection = inspections[index];
          return _buildInspectionTile(context, inspection);
        },
      ),
    );
  }

  Widget _buildInspectionTile(BuildContext context, dynamic inspection) {
    // Handle both InspectionSummary and Map types
    final String id = inspection is InspectionSummary 
        ? inspection.id 
        : inspection['id'] ?? '';
    final String branchOfficeName = inspection is InspectionSummary 
        ? inspection.branchOfficeName 
        : inspection['branch_office_name'] ?? '';
    final String inspectorName = inspection is InspectionSummary 
        ? inspection.inspectorName 
        : inspection['inspector_name'] ?? '';
    final DateTime inspectionDate = inspection is InspectionSummary 
        ? inspection.inspectionDate 
        : DateTime.tryParse(inspection['inspection_date'] ?? '') ?? DateTime.now();
    final bool isCompleted = inspection is InspectionSummary 
        ? inspection.isCompleted 
        : (inspection['is_completed'] ?? 0) == 1;
    final double? totalScore = inspection is InspectionSummary 
        ? inspection.totalScore 
        : inspection['total_score']?.toDouble();
    final String? grade = inspection is InspectionSummary 
        ? inspection.grade 
        : inspection['grade'];

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: isCompleted 
            ? Colors.green.withOpacity(0.1) 
            : Colors.orange.withOpacity(0.1),
        child: Icon(
          isCompleted ? Icons.check_circle : Icons.pending,
          color: isCompleted ? Colors.green : Colors.orange,
        ),
      ),
      title: Text(
        branchOfficeName,
        style: Theme.of(context).textTheme.titleMedium,
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Inspector: $inspectorName'),
          Text('Date: ${AppUtils.formatDate(inspectionDate)}'),
          if (isCompleted && grade != null)
            Row(
              children: [
                Icon(
                  AppUtils.getGradeIcon(grade),
                  size: 16,
                  color: AppUtils.getGradeColor(grade),
                ),
                const SizedBox(width: 4),
                Text(
                  grade,
                  style: TextStyle(
                    color: AppUtils.getGradeColor(grade),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (totalScore != null) ...[
                  const SizedBox(width: 8),
                  Text('(${totalScore.toStringAsFixed(1)}%)'),
                ],
              ],
            ),
        ],
      ),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isCompleted)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'Completed',
                style: TextStyle(
                  color: Colors.green[700],
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            )
          else
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'In Progress',
                style: TextStyle(
                  color: Colors.orange[700],
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          const SizedBox(width: 8),
          const Icon(Icons.chevron_right),
        ],
      ),
      onTap: () {
        AppRoutes.navigateToInspectionForm(
          context,
          inspectionId: id,
          isReadOnly: isCompleted,
        );
      },
    );
  }
}
