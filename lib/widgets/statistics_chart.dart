import 'package:flutter/material.dart';
import '../main.dart';

class Statistics<PERSON>hart extends StatelessWidget {
  final List<Map<String, dynamic>> gradeDistribution;

  const StatisticsChart({
    super.key,
    required this.gradeDistribution,
  });

  @override
  Widget build(BuildContext context) {
    if (gradeDistribution.isEmpty) {
      return Container(
        height: 200,
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bar_chart,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No data available',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    // Calculate total for percentage calculation
    final int total = gradeDistribution.fold<int>(
      0,
      (sum, item) => sum + (item['count'] as int? ?? 0),
    );

    return Container(
      height: 200,
      child: Column(
        children: [
          // Chart bars
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: gradeDistribution.map((item) {
                final String grade = item['grade'] ?? '';
                final int count = item['count'] ?? 0;
                final double percentage = total > 0 ? (count / total) * 100 : 0;
                final Color color = AppUtils.getGradeColor(grade);

                return Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // Count label
                        Text(
                          count.toString(),
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        // Bar
                        Container(
                          width: double.infinity,
                          height: (percentage / 100) * 120 + 20, // Min height 20
                          decoration: BoxDecoration(
                            color: color,
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(4),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        // Grade label
                        Text(
                          _getShortGradeName(grade),
                          style: Theme.of(context).textTheme.labelSmall,
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          const SizedBox(height: 16),
          // Legend
          Wrap(
            spacing: 16,
            runSpacing: 8,
            children: gradeDistribution.map((item) {
              final String grade = item['grade'] ?? '';
              final int count = item['count'] ?? 0;
              final Color color = AppUtils.getGradeColor(grade);

              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '$grade ($count)',
                    style: Theme.of(context).textTheme.labelSmall,
                  ),
                ],
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  String _getShortGradeName(String grade) {
    switch (grade.toLowerCase()) {
      case 'excellent':
        return 'Excellent';
      case 'very good':
        return 'Very\nGood';
      case 'good':
        return 'Good';
      case 'satisfactory':
        return 'Satisfactory';
      case 'needs improvement':
        return 'Needs\nImprovement';
      case 'poor':
        return 'Poor';
      default:
        return grade;
    }
  }
}
