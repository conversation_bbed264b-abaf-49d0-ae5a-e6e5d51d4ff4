import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import '../models/inspection_data.dart';
import '../models/final_sections.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    // Initialize FFI for desktop platforms
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }

    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, 'bo_inspection.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
    );
  }

  Future<void> _createDatabase(Database db, int version) async {
    // Create inspections table
    await db.execute('''
      CREATE TABLE inspections (
        id TEXT PRIMARY KEY,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        inspector_name TEXT NOT NULL,
        inspector_designation TEXT NOT NULL,
        inspection_date TEXT NOT NULL,
        is_completed INTEGER NOT NULL DEFAULT 0,
        data TEXT NOT NULL
      )
    ''');

    // Create inspection summaries table for quick access
    await db.execute('''
      CREATE TABLE inspection_summaries (
        id TEXT PRIMARY KEY,
        branch_office_name TEXT NOT NULL,
        inspector_name TEXT NOT NULL,
        inspection_date TEXT NOT NULL,
        is_completed INTEGER NOT NULL DEFAULT 0,
        total_score REAL,
        grade TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT
      )
    ''');

    // Create indexes for better performance
    await db.execute('''
      CREATE INDEX idx_inspections_date ON inspections(inspection_date)
    ''');

    await db.execute('''
      CREATE INDEX idx_inspections_inspector ON inspections(inspector_name)
    ''');

    await db.execute('''
      CREATE INDEX idx_summaries_date ON inspection_summaries(inspection_date)
    ''');

    await db.execute('''
      CREATE INDEX idx_summaries_branch ON inspection_summaries(branch_office_name)
    ''');
  }

  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < 2) {
      // Add new columns or tables for version 2
    }
  }

  // CRUD Operations for Inspections

  Future<String> saveInspection(InspectionData inspection) async {
    final db = await database;
    
    final inspectionMap = {
      'id': inspection.id,
      'created_at': inspection.createdAt.toIso8601String(),
      'updated_at': inspection.updatedAt?.toIso8601String(),
      'inspector_name': inspection.inspectorName,
      'inspector_designation': inspection.inspectorDesignation,
      'inspection_date': inspection.inspectionDate.toIso8601String(),
      'is_completed': inspection.isCompleted ? 1 : 0,
      'data': jsonEncode(inspection.toJson()),
    };

    await db.insert(
      'inspections',
      inspectionMap,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    // Also update the summary table
    await _updateInspectionSummary(inspection);

    return inspection.id;
  }

  Future<void> _updateInspectionSummary(InspectionData inspection) async {
    final db = await database;
    
    final summary = InspectionSummary(
      id: inspection.id,
      branchOfficeName: inspection.administration.branchOfficeName,
      inspectorName: inspection.inspectorName,
      inspectionDate: inspection.inspectionDate,
      isCompleted: inspection.isCompleted,
      totalScore: inspection.isCompleted ? inspection.conclusion.percentage : null,
      grade: inspection.isCompleted ? inspection.conclusion.overallGrade : null,
      createdAt: inspection.createdAt,
      updatedAt: inspection.updatedAt,
    );

    await db.insert(
      'inspection_summaries',
      summary.toJson(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<InspectionData?> getInspection(String id) async {
    final db = await database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      'inspections',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    final data = jsonDecode(map['data']) as Map<String, dynamic>;
    
    return InspectionData.fromJson(data);
  }

  Future<List<InspectionSummary>> getAllInspectionSummaries() async {
    final db = await database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      'inspection_summaries',
      orderBy: 'inspection_date DESC',
    );

    return maps.map((map) => InspectionSummary.fromJson(map)).toList();
  }

  Future<List<InspectionSummary>> getInspectionSummariesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final db = await database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      'inspection_summaries',
      where: 'inspection_date BETWEEN ? AND ?',
      whereArgs: [
        startDate.toIso8601String(),
        endDate.toIso8601String(),
      ],
      orderBy: 'inspection_date DESC',
    );

    return maps.map((map) => InspectionSummary.fromJson(map)).toList();
  }

  Future<List<InspectionSummary>> searchInspections(String query) async {
    final db = await database;
    
    final List<Map<String, dynamic>> maps = await db.query(
      'inspection_summaries',
      where: '''
        branch_office_name LIKE ? OR 
        inspector_name LIKE ? OR 
        grade LIKE ?
      ''',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'inspection_date DESC',
    );

    return maps.map((map) => InspectionSummary.fromJson(map)).toList();
  }

  Future<void> deleteInspection(String id) async {
    final db = await database;
    
    await db.delete(
      'inspections',
      where: 'id = ?',
      whereArgs: [id],
    );

    await db.delete(
      'inspection_summaries',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<void> updateInspection(InspectionData inspection) async {
    final updatedInspection = inspection.copyWith(
      updatedAt: DateTime.now(),
    );
    
    await saveInspection(updatedInspection);
  }

  // Statistics and Analytics

  Future<Map<String, dynamic>> getInspectionStatistics() async {
    final db = await database;
    
    final totalCount = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM inspection_summaries'),
    ) ?? 0;

    final completedCount = Sqflite.firstIntValue(
      await db.rawQuery(
        'SELECT COUNT(*) FROM inspection_summaries WHERE is_completed = 1',
      ),
    ) ?? 0;

    final averageScore = await db.rawQuery(
      'SELECT AVG(total_score) as avg_score FROM inspection_summaries WHERE total_score IS NOT NULL',
    );

    final gradeDistribution = await db.rawQuery(
      'SELECT grade, COUNT(*) as count FROM inspection_summaries WHERE grade IS NOT NULL GROUP BY grade',
    );

    return {
      'totalInspections': totalCount,
      'completedInspections': completedCount,
      'pendingInspections': totalCount - completedCount,
      'averageScore': averageScore.first['avg_score'] ?? 0.0,
      'gradeDistribution': gradeDistribution,
    };
  }

  Future<List<Map<String, dynamic>>> getMonthlyInspectionCounts() async {
    final db = await database;
    
    return await db.rawQuery('''
      SELECT 
        strftime('%Y-%m', inspection_date) as month,
        COUNT(*) as count
      FROM inspection_summaries 
      GROUP BY strftime('%Y-%m', inspection_date)
      ORDER BY month DESC
      LIMIT 12
    ''');
  }

  // Database maintenance

  Future<void> clearAllData() async {
    final db = await database;
    
    await db.delete('inspections');
    await db.delete('inspection_summaries');
  }

  Future<void> closeDatabase() async {
    final db = await database;
    await db.close();
    _database = null;
  }

  Future<String> getDatabasePath() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    return join(documentsDirectory.path, 'bo_inspection.db');
  }

  Future<int> getDatabaseSize() async {
    final path = await getDatabasePath();
    final file = File(path);
    if (await file.exists()) {
      return await file.length();
    }
    return 0;
  }
}
