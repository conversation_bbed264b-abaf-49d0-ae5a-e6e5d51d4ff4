import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/inspection_provider.dart';
import '../main.dart';

class InspectionFormScreen extends StatefulWidget {
  final String? inspectionId;
  final bool isReadOnly;

  const InspectionFormScreen({
    super.key,
    this.inspectionId,
    this.isReadOnly = false,
  });

  @override
  State<InspectionFormScreen> createState() => _InspectionFormScreenState();
}

class _InspectionFormScreenState extends State<InspectionFormScreen> {
  int _currentSectionIndex = 0;
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    if (widget.inspectionId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<InspectionProvider>().loadInspection(widget.inspectionId!);
      });
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isReadOnly ? 'View Inspection' : 'Edit Inspection'),
        actions: [
          if (!widget.isReadOnly)
            IconButton(
              icon: const Icon(Icons.save),
              onPressed: _saveInspection,
            ),
        ],
      ),
      body: Consumer<InspectionProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (provider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error: ${provider.errorMessage}',
                    style: Theme.of(context).textTheme.titleMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      if (widget.inspectionId != null) {
                        provider.loadInspection(widget.inspectionId!);
                      }
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (provider.currentInspection == null) {
            return const Center(
              child: Text('Inspection not found'),
            );
          }

          return Column(
            children: [
              // Section Navigation
              _buildSectionNavigation(),
              
              // Progress Indicator
              _buildProgressIndicator(),
              
              // Form Content
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: AppConstants.sectionNames.length,
                  onPageChanged: (index) {
                    setState(() {
                      _currentSectionIndex = index;
                    });
                  },
                  itemBuilder: (context, index) {
                    return _buildSectionForm(index);
                  },
                ),
              ),
              
              // Navigation Buttons
              _buildNavigationButtons(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionNavigation() {
    return Container(
      height: 60,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: AppConstants.sectionNames.length,
        itemBuilder: (context, index) {
          final isSelected = index == _currentSectionIndex;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
            child: FilterChip(
              selected: isSelected,
              label: Text(
                AppConstants.sectionNames[index],
                style: TextStyle(
                  fontSize: 12,
                  color: isSelected ? Colors.white : null,
                ),
              ),
              avatar: Icon(
                AppConstants.sectionIcons[index],
                size: 16,
                color: isSelected ? Colors.white : null,
              ),
              onSelected: (selected) {
                if (selected) {
                  _navigateToSection(index);
                }
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Section ${_currentSectionIndex + 1} of ${AppConstants.sectionNames.length}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Text(
                '${((_currentSectionIndex + 1) / AppConstants.sectionNames.length * 100).toInt()}%',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: (_currentSectionIndex + 1) / AppConstants.sectionNames.length,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionForm(int sectionIndex) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section Header
              Row(
                children: [
                  Icon(
                    AppConstants.sectionIcons[sectionIndex],
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      AppConstants.sectionNames[sectionIndex],
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              
              // Section Content
              _buildSectionContent(sectionIndex),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionContent(int sectionIndex) {
    // For now, show a placeholder for each section
    // In a complete implementation, each section would have its specific form fields
    return Column(
      children: [
        Container(
          height: 200,
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                AppConstants.sectionIcons[sectionIndex],
                size: 48,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'Section ${sectionIndex + 1} Form',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Form fields for ${AppConstants.sectionNames[sectionIndex]} will be implemented here',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          if (_currentSectionIndex > 0)
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _previousSection,
                icon: const Icon(Icons.chevron_left),
                label: const Text('Previous'),
              ),
            ),
          if (_currentSectionIndex > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _currentSectionIndex < AppConstants.sectionNames.length - 1
                  ? _nextSection
                  : _completeInspection,
              icon: Icon(_currentSectionIndex < AppConstants.sectionNames.length - 1
                  ? Icons.chevron_right
                  : Icons.check),
              label: Text(_currentSectionIndex < AppConstants.sectionNames.length - 1
                  ? 'Next'
                  : 'Complete'),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToSection(int index) {
    setState(() {
      _currentSectionIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _previousSection() {
    if (_currentSectionIndex > 0) {
      _navigateToSection(_currentSectionIndex - 1);
    }
  }

  void _nextSection() {
    if (_currentSectionIndex < AppConstants.sectionNames.length - 1) {
      _navigateToSection(_currentSectionIndex + 1);
    }
  }

  Future<void> _saveInspection() async {
    final provider = context.read<InspectionProvider>();
    final success = await provider.saveCurrentInspection();
    
    if (success && mounted) {
      AppUtils.showSnackBar(
        context,
        'Inspection saved successfully!',
      );
    }
  }

  Future<void> _completeInspection() async {
    if (widget.isReadOnly) return;

    final confirmed = await AppUtils.showConfirmDialog(
      context,
      'Complete Inspection',
      'Are you sure you want to mark this inspection as complete? This action cannot be undone.',
    );

    if (confirmed && mounted) {
      // TODO: Mark inspection as complete and calculate final scores
      AppUtils.showSnackBar(
        context,
        'Inspection completed successfully!',
      );
      Navigator.of(context).pop();
    }
  }
}
