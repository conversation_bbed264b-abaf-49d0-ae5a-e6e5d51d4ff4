import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/inspection_provider.dart';
import '../main.dart';

class NewInspectionScreen extends StatefulWidget {
  const NewInspectionScreen({super.key});

  @override
  State<NewInspectionScreen> createState() => _NewInspectionScreenState();
}

class _NewInspectionScreenState extends State<NewInspectionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _inspectorNameController = TextEditingController();
  final _inspectorDesignationController = TextEditingController();
  final _branchOfficeNameController = TextEditingController();
  final _accountOfficeNameController = TextEditingController();
  final _headOfficeNameController = TextEditingController();
  
  DateTime _inspectionDate = DateTime.now();

  @override
  void dispose() {
    _inspectorNameController.dispose();
    _inspectorDesignationController.dispose();
    _branchOfficeNameController.dispose();
    _accountOfficeNameController.dispose();
    _headOfficeNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('New Inspection'),
      ),
      body: Consumer<InspectionProvider>(
        builder: (context, provider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Theme.of(context).primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.add_circle_outline,
                              size: 32,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Create New Inspection',
                                  style: Theme.of(context).textTheme.headlineSmall,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Fill in the basic information to start a new inspection',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Inspector Information
                  Text(
                    'Inspector Information',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  
                  TextFormField(
                    controller: _inspectorNameController,
                    decoration: const InputDecoration(
                      labelText: 'Inspector Name *',
                      hintText: 'Enter inspector name',
                      prefixIcon: Icon(Icons.person),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Inspector name is required';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  TextFormField(
                    controller: _inspectorDesignationController,
                    decoration: const InputDecoration(
                      labelText: 'Inspector Designation *',
                      hintText: 'Enter inspector designation',
                      prefixIcon: Icon(Icons.work),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Inspector designation is required';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  // Inspection Date
                  InkWell(
                    onTap: _selectInspectionDate,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'Inspection Date *',
                        prefixIcon: Icon(Icons.calendar_today),
                      ),
                      child: Text(
                        AppUtils.formatDate(_inspectionDate),
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ),
                  ),
                  const SizedBox(height: 32),

                  // Branch Office Information
                  Text(
                    'Branch Office Information',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  
                  TextFormField(
                    controller: _branchOfficeNameController,
                    decoration: const InputDecoration(
                      labelText: 'Branch Office Name *',
                      hintText: 'Enter branch office name',
                      prefixIcon: Icon(Icons.business),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Branch office name is required';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  TextFormField(
                    controller: _accountOfficeNameController,
                    decoration: const InputDecoration(
                      labelText: 'Account Office Name *',
                      hintText: 'Enter account office name',
                      prefixIcon: Icon(Icons.account_balance),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Account office name is required';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  
                  TextFormField(
                    controller: _headOfficeNameController,
                    decoration: const InputDecoration(
                      labelText: 'Head Office Name *',
                      hintText: 'Enter head office name',
                      prefixIcon: Icon(Icons.location_city),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Head office name is required';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 32),

                  // Create Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: provider.isSaving ? null : _createInspection,
                      child: provider.isSaving
                          ? const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                ),
                                SizedBox(width: 12),
                                Text('Creating...'),
                              ],
                            )
                          : const Text('Create Inspection'),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Error Message
                  if (provider.errorMessage != null)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        border: Border.all(color: Colors.red[300]!),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error_outline, color: Colors.red[700]),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              provider.errorMessage!,
                              style: TextStyle(color: Colors.red[700]),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _selectInspectionDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _inspectionDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );
    if (picked != null && picked != _inspectionDate) {
      setState(() {
        _inspectionDate = picked;
      });
    }
  }

  Future<void> _createInspection() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final provider = context.read<InspectionProvider>();
    
    final inspectionId = await provider.createNewInspection(
      inspectorName: _inspectorNameController.text.trim(),
      inspectorDesignation: _inspectorDesignationController.text.trim(),
      inspectionDate: _inspectionDate,
      branchOfficeName: _branchOfficeNameController.text.trim(),
      accountOfficeName: _accountOfficeNameController.text.trim(),
      headOfficeName: _headOfficeNameController.text.trim(),
    );

    if (inspectionId != null && mounted) {
      AppUtils.showSnackBar(
        context,
        'Inspection created successfully!',
      );
      
      // Navigate to the inspection form
      AppRoutes.navigateToInspectionForm(
        context,
        inspectionId: inspectionId,
      );
    }
  }
}
